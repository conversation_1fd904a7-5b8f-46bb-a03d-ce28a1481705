package com.stock.service.platform.agreementLog.service;

import com.stock.core.service.BaseService;
import com.stock.service.platform.agreementLog.dao.AgreementMapper;
import com.stock.service.platform.agreementLog.dto.AgreementDto;
import com.stock.service.platform.agreementLog.dto.LoginAgreementLog;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Service
@Transactional(rollbackFor = Exception.class)
public class AgreementService extends BaseService {

    @Resource
    private AgreementMapper agreementMapper;

    public List<AgreementDto> getAgreement() {
        return agreementMapper.getAgreement();
    }

    public void saveLoginAgreementLog(HttpServletRequest request, List<AgreementDto> agreementDtos) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        List<LoginAgreementLog> list = new ArrayList<>();
        LoginAgreementLog loginAgreementLog = null;
        for (AgreementDto agreementDto : agreementDtos) {
            loginAgreementLog = new LoginAgreementLog();
            loginAgreementLog.setAgreementId(agreementDto.getId());
            loginAgreementLog.setLoginIp(ip);
            loginAgreementLog.setUserId(getUserInfo().getUserId());
            loginAgreementLog.setUserName(getUserInfo().getUsername());
            loginAgreementLog.setCreateUser(getUserInfo().getUserId());
            loginAgreementLog.setCreateTime(new Date());
            list.add(loginAgreementLog);
        }
        agreementMapper.saveLoginAgreementLogBatch(list);
    }
}
